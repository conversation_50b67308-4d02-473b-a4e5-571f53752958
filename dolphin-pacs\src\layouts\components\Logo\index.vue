<script lang="ts" setup>
import logoPng from "@@/assets/images/logo/logo.png?url"
import logoSvg from "@@/assets/images/logo/logo.svg?url"
import { useLayoutMode } from "@@/composables/useLayoutMode"

interface Props {
  collapse?: boolean
}

const { collapse = true } = defineProps<Props>()

const { isTop } = useLayoutMode()
</script>

<template>
  <div class="layout-logo-container" :class="{ 'collapse': collapse, 'layout-mode-top': isTop }">
    <transition name="layout-logo-fade">
      <router-link v-if="collapse" key="collapse" to="/">
        <img :src="logoPng" class="layout-logo">
      </router-link>
      <router-link v-else key="expand" to="/" class="layout-logo-dual">
        <img :src="logoPng" class="layout-logo-png">
        <img :src="logoSvg" class="layout-logo-svg">
      </router-link>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
.layout-logo-container {
  position: relative;
  width: 100%;
  height: var(--v3-header-height);
  line-height: var(--v3-header-height);
  text-align: center;
  overflow: hidden;

  .layout-logo {
    display: none;
  }

  .layout-logo-dual {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 12px; // 两个logo之间的间距

    .layout-logo-png,
    .layout-logo-svg {
      height: 80%; // 相对于容器高度的80%
      max-height: 40px; // 最大高度限制
      width: auto; // 保持宽高比
      vertical-align: middle;
    }
  }
}

.layout-mode-top {
  height: var(--v3-navigationbar-height);
  line-height: var(--v3-navigationbar-height);
}

.collapse {
  .layout-logo {
    width: 32px;
    height: 32px;
    vertical-align: middle;
    display: inline-block;
  }

  .layout-logo-dual {
    display: none;
  }
}
</style>
